<script>
    import { enhance } from "$app/forms";
    import { goto } from "$app/navigation";
    import { invalidateAll } from "$app/navigation";
    import { timeAgo } from '$lib/utils/dateUtils';

    export let data;

    // team, devices, wallets, statistics are plain objects/arrays from data
    let team = data.team ? data.team : null;
    let devices = data.devices || [];
    let wallets = data.wallets || [];
    let statistics = data.statistics || { wallets: 0, transactions: 0, devices: 0 };
    let showDeleteConfirmation = false;

    // Initialize form fields for editable properties
    let charge_amount = team?.charge_amount;
    let balance = team?.balance;

    let successMessage = null;
    let errorMessage = null;
    let isSubmitting = false;

    // Update local team variable when data.team changes
    $: team = data.team ? data.team : null;

    // Update form fields when the local team variable changes
    $: if (team) {
        charge_amount = team.charge_amount;
        balance = team.balance;
        // Clear messages on team change
        successMessage = null;
        errorMessage = null;
    } else {
        // Reset fields if team data is no longer available
        charge_amount = undefined;
        balance = undefined;
    }

    // Enhance options for the form
    const enhanceOptions = ({
        formElement,
        formData,
        action,
        cancel,
        controller,
        submitter
    }) => {
        isSubmitting = true;
        successMessage = null;
        errorMessage = null;

        return async ({ result }) => {
            isSubmitting = false;
            if (result.type === "success") {
                const actionData = result.data;
                if (actionData?.success) {
                    successMessage = "Team updated successfully!";
                    if (team?.id) {
                        goto(`/admin/teams/${team.id}`);
                    } else {
                        goto("/admin/teams");
                    }
                } else if (actionData?.error) {
                    errorMessage = actionData.error;
                } else {
                    errorMessage = "An unexpected success response was received.";
                }
            } else if (result.type === "failure") {
                const actionData = result.data;
                errorMessage = actionData?.error || "Failed to update team.";
            } else if (result.type === "error") {
                errorMessage = result.error?.message || `Server error with status ${result.status}`;
                console.error("Server error result:", result);
            } else if (result.type === "redirect") {
                successMessage = null;
                errorMessage = null;
                invalidateAll().then(() => {
                    goto(result.location);
                });
            }
        };
    };

    function formatDate(dateString) {
        if (!dateString) return "-";
        try {
            const date = new Date(dateString);
            return date.toLocaleString(undefined, {
                year: "numeric",
                month: "numeric",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
            });
        } catch {
            return "Invalid date";
        }
    }

    function getRoleBadgeClass(role) {
        if (!role) return 'role-badge';
        switch(role.toLowerCase()) {
            case 'admin':
                return 'role-badge role-admin';
            case 'user':
                return 'role-badge role-user';
            default:
                return 'role-badge';
        }
    }

</script>

<svelte:head>
  <title>Phantom | Team Details</title>
</svelte:head>

<div class="admin-container">
    {#if team}
        <div class="admin-card">
            <h1 class="admin-title">{team.id} - Edit Team</h1>

            <hr style="margin-top: 0.5rem; border-color: #333;" />

            <div class="team-info-grid">
                <div class="team-info-item">
                    <strong>ID:</strong>
                    {team.id || "N/A"}
                    {#if team.internal_id}
                        <div style="font-size: 0.8em; margin-top: 0.2em;">
                            <strong>Internal ID:</strong>
                            {team.internal_id}
                        </div>
                    {/if}
                </div>

                <div class="team-info-item">
                    <strong>Charge Amount:</strong>
                    ${team.charge_amount.toFixed(2)}
                </div>

                <div class="team-info-item">
                    <strong>Balance:</strong>
                    ${team.balance.toFixed(2)}
                </div>

                <div class="team-info-item date-group">
                    <div>
                        <strong>Created At:</strong>
                        {formatDate(team.created_at)}
                    </div>
                    {#if team.next_charge_at}
                        <div>
                            <strong>Next Charge At:</strong>
                            {formatDate(team.next_charge_at)}
                        </div>
                    {/if}
                </div>

                {#if team.owner_id}
                    <div class="team-info-item">
                        <strong>Owner ID:</strong>
                        {team.owner_id}
                    </div>
                {/if}
                {#if team.owner_internal_id}
                    <div class="team-info-item">
                        <strong style="font-size: 0.9em;"
                            >Owner Internal ID:</strong
                        >
                        {team.owner_internal_id}
                    </div>
                {/if}
            </div>

            <hr
                style="margin-top: -0.5rem; margin-bottom: 1rem; border-color: #333;"
            />

            <form method="POST" action="?/update" use:enhance={enhanceOptions}>
                <div class="horizontal-layout">
                    <div class="form-group">
                        <label for="id" class="form-label">ID</label>
                        <input
                            id="id"
                            class="form-input"
                            type="text"
                            bind:value={team.id}
                            placeholder="Enter team ID"
                            name="id"
                            required
                        />
                    </div>
                    <div class="form-group">
                        <label for="charge_amount" class="form-label">Charge Amount</label>
                        <input
                            id="charge_amount"
                            class="form-input"
                            type="number"
                            step="0.01"
                            min="0"
                            bind:value={charge_amount}
                            placeholder="Enter charge amount"
                            name="charge_amount"
                            required
                        />
                    </div>
                    <div class="form-group">
                        <label for="balance" class="form-label">Balance</label>
                        <input
                            id="balance"
                            class="form-input"
                            type="number"
                            step="0.01"
                            min="0"
                            bind:value={balance}
                            placeholder="Enter balance"
                            name="balance"
                            required
                        />
                    </div>
                </div>

                <div class="form-actions">
                    <button class="button button-secondary" on:click|preventDefault={()=>goto("/admin/teams")} disabled={isSubmitting}
                        >Cancel</button
                    >
                    <button type="submit" class="button" disabled={isSubmitting}
                        >Save Changes</button
                    >
                </div>
            </form>

            {#if successMessage}
                <div class="message success message-container">
                    {successMessage}
                </div>
            {/if}

            {#if errorMessage}
                <div class="message error message-container">
                    {errorMessage}
                </div>
            {/if}

            <!-- Devices Section -->
            <div class="section">
                <h2>Devices ({devices.length})</h2>
                {#if devices.length > 0}
                    <table class="client-table">
                        <thead>
                            <tr>
                                <th style="padding: 4px 4px;">IP</th>
                                <th style="padding: 4px 4px;">Nickname</th>
                                <th style="padding: 4px 4px;">Role</th>
                                <th style="padding: 4px 4px;">Last Active</th>
                                <th style="padding: 4px 4px;">Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            {#each devices as device}
                                <tr>
                                    <td style="padding: 4px 4px; font-weight: bold; color: #ffd700;">{device.ip || '-'}</td>
                                    <td style="padding: 4px 4px;">{device.nickname || '-'}</td>
                                    <td style="padding: 4px 4px;">
                                        {#if device.role}
                                            <span class={getRoleBadgeClass(device.role)}>{device.role}</span>
                                        {:else}
                                            <span class="text-muted">-</span>
                                        {/if}
                                    </td>
                                    <td style="padding: 4px 4px;">{timeAgo(device.last_auth_at, 'Never')}</td>
                                    <td style="padding: 4px 4px;">{timeAgo(device.created_at)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {:else}
                    <div class="empty-state">
                        <p>No devices found for this team.</p>
                    </div>
                {/if}
            </div>

            <!-- Wallets Section -->
            <div class="section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h2>Wallets ({wallets.length})</h2>
                    <a href="/admin/teams/{team.id}/transactions" class="button button-secondary" style="text-decoration: none;">
                        View Transactions
                    </a>
                </div>
                {#if wallets.length > 0}
                    <table class="client-table">
                        <thead>
                            <tr>
                                <th style="padding: 4px 4px;">Currency</th>
                                <th style="padding: 4px 4px;">Address</th>
                            </tr>
                        </thead>
                        <tbody>
                            {#each wallets as wallet}
                                <tr>
                                    <td style="padding: 4px 4px; font-weight: bold; color: #ffd700;">{wallet.currency}</td>
                                    <td style="padding: 4px 4px; font-family: monospace; font-size: 0.9em;">{wallet.address}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {:else}
                    <div class="empty-state">
                        <p>No wallets found for this team.</p>
                        <p style="font-size: 0.9em; margin-top: 0.5rem; color: #a0aec0;">
                            Wallets are automatically generated when a team is created.
                        </p>
                    </div>
                {/if}
            </div>

            <!-- Delete Team Section -->
            <div class="section danger-section">
                <h2>Danger Zone</h2>
                <div class="danger-content">
                    <div class="danger-info">
                        <h3>Delete Team</h3>
                        <p>Permanently delete this team and all associated data. This action cannot be undone.</p>

                        <div class="deletion-summary">
                            <h4>The following data will be deleted:</h4>
                            <ul>
                                <li>{statistics.devices} device{statistics.devices !== 1 ? 's' : ''}</li>
                                <li>{statistics.wallets} wallet{statistics.wallets !== 1 ? 's' : ''}</li>
                                <li>{statistics.transactions} transaction{statistics.transactions !== 1 ? 's' : ''}</li>
                            </ul>
                        </div>
                    </div>

                    {#if !showDeleteConfirmation}
                        <button
                            class="button button-danger"
                            on:click={() => showDeleteConfirmation = true}
                        >
                            Delete Team
                        </button>
                    {:else}
                        <div class="delete-confirmation">
                            <p><strong>Are you sure?</strong> This will permanently delete team "{team?.id}" and all associated data.</p>
                            <div class="confirmation-buttons">
                                <button
                                    class="button button-secondary"
                                    on:click={() => showDeleteConfirmation = false}
                                >
                                    Cancel
                                </button>
                                <form method="POST" action="?/delete" style="display: inline;" use:enhance={enhanceOptions}>
                                    <button type="submit" class="button button-danger">
                                        Yes, Delete Team
                                    </button>
                                </form>
                            </div>
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    {:else}
        <p class="empty-state">
            Loading team data or team not found...
        </p>
    {/if}
</div>


